<?php
// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Generate a unique quote ID if not already set
if (!isset($_SESSION['quote_id'])) {
    $_SESSION['quote_id'] = 'QUOTE-' . bin2hex(random_bytes(6));
}

// Get vehicle information from session
$vehicle_year = $_SESSION['vehicle_year'] ?? '';
$vehicle_brand = $_SESSION['vehicle_brand'] ?? '';
$vehicle_model = $_SESSION['vehicle_model'] ?? '';

// Get location information
$origin_city = $_SESSION['origin_city'] ?? '';
$origin_state = $_SESSION['origin_state'] ?? '';
$origin_postal_code = $_SESSION['origin_postal_code'] ?? '';

$destination_city = $_SESSION['destination_city'] ?? '';
$destination_state = $_SESSION['destination_state'] ?? '';
$destination_postal_code = $_SESSION['destination_postal_code'] ?? '';

// Get transport details
$transport_type = isset($_SESSION['transport_type']) ? ($_SESSION['transport_type'] == 1 ? 'Open Carrier' : 'Enclosed Carrier') : 'Open Carrier';
$vehicle_condition = isset($_SESSION['vehicle_inop']) ? ($_SESSION['vehicle_inop'] == 0 ? 'Operable' : 'Inoperable') : 'Operable';

// Estimated transit time (can be calculated based on distance or set as default)
$estimated_transit = "2-3 Days";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shipment Summary</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f5f5f7;
            margin: 0;
            padding: 0;
        }
        .shipment-container {
            max-width: 800px;
            margin: 20px auto;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .shipment-header {
            background-color: #f5f5f7;
            padding: 15px 20px;
            border-bottom: 1px solid #e1e1e1;
        }
        .shipment-header h2 {
            margin: 0;
            font-size: 24px;
            color: #333;
        }
        .shipment-content {
            padding: 0;
        }
        .summary-row {
            display: flex;
            justify-content: space-between;
            padding: 15px 20px;
            border-bottom: 1px solid #e1e1e1;
        }
        .summary-row:last-child {
            border-bottom: none;
        }
        .summary-label {
            font-size: 16px;
            color: #666;
        }
        .summary-value {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            text-align: right;
        }
        @media (max-width: 768px) {
            .shipment-container {
                margin: 10px;
                border-radius: 6px;
            }
            .summary-row {
                padding: 12px 15px;
            }
            .summary-label, .summary-value {
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="shipment-container">
        <div class="shipment-header">
            <h2>Shipment Summary:</h2>
        </div>
        <div class="shipment-content">
            <div class="summary-row">
                <div class="summary-label">Quote ID</div>
                <div class="summary-value"><?php echo htmlspecialchars($_SESSION['quote_id']); ?></div>
            </div>
            
            <div class="summary-row">
                <div class="summary-label">Pickup From</div>
                <div class="summary-value"><?php echo htmlspecialchars("$origin_city, $origin_state $origin_postal_code"); ?></div>
            </div>
            
            <div class="summary-row">
                <div class="summary-label">Deliver To</div>
                <div class="summary-value"><?php echo htmlspecialchars("$destination_city, $destination_state $destination_postal_code"); ?></div>
            </div>
            
            <div class="summary-row">
                <div class="summary-label">Vehicle</div>
                <div class="summary-value"><?php echo htmlspecialchars("$vehicle_year $vehicle_brand $vehicle_model"); ?></div>
            </div>
            
            <div class="summary-row">
                <div class="summary-label">Transport Type</div>
                <div class="summary-value"><?php echo htmlspecialchars($transport_type); ?></div>
            </div>
            
            <div class="summary-row">
                <div class="summary-label">Condition</div>
                <div class="summary-value"><?php echo htmlspecialchars($vehicle_condition); ?></div>
            </div>
            
            <div class="summary-row">
                <div class="summary-label">Estimated Transit</div>
                <div class="summary-value"><?php echo htmlspecialchars($estimated_transit); ?></div>
            </div>
        </div>
    </div>
</body>
</html>
