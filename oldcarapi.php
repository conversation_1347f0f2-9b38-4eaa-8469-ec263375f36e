<?php
$endpoint = $_GET['endpoint'] ?? '';
$make = $_GET['make'] ?? '';
$model = $_GET['model'] ?? '';
$year = $_GET['year'] ?? '';
$attribute = $_GET['attribute'] ?? '';
$model_id = $_GET['model_id'] ?? '';

// API credentials
$apiToken = "c0ac9104-2b74-4f58-a338-d354a75cc894";
$apiSecret = "ae3df83d3eee3268f658cc446cc5cc30";

// JWT cache file path
$jwtCachePath = __DIR__ . '/cache/carapi_jwt.txt';
$jwtCacheDir = dirname($jwtCachePath);

// Create cache directory if it doesn't exist
if (!file_exists($jwtCacheDir)) {
    mkdir($jwtCacheDir, 0755, true);
}

// Get or generate JWT token
function getJwtToken($apiToken, $apiSecret, $jwtCachePath) {
    // Check if we have a cached token that's still valid
    if (file_exists($jwtCachePath)) {
        $jwt = file_get_contents($jwtCachePath);
        // Check if token is still valid (not expired)
        $fileTime = filemtime($jwtCachePath);
        if (time() - $fileTime < 86400) { // 24 hours
            return $jwt;
        }
    }
    
    // Get a new token
    $ch = curl_init('https://carapi.app/api/auth/login');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: text/plain'
    ]);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
        'api_token' => $apiToken,
        'api_secret' => $apiSecret
    ]));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $jwt = curl_exec($ch);
    curl_close($ch);
    
    // Cache the token
    if ($jwt) {
        file_put_contents($jwtCachePath, $jwt);
    }
    
    return $jwt;
}

$jwt = getJwtToken($apiToken, $apiSecret, $jwtCachePath);

// Set up the request headers with JWT authentication
$headers = [
    "Accept: application/json",
    "Authorization: Bearer " . $jwt
];

// Special handling for makes endpoint
if ($endpoint === 'makes') {
    $url = "https://carapi.app/api/makes?sort=name&limit=1278";
    
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    $response = curl_exec($ch);
    
    if (curl_errno($ch)) {
        header('Content-Type: application/json');
        echo json_encode(['error' => 'API request failed: ' . curl_error($ch)]);
        exit;
    }
    
    curl_close($ch);
    
    // Return the makes data
    header('Content-Type: application/json');
    echo $response;
    exit;
}

// Special handling for models endpoint
if ($endpoint === 'models' && $make && $year) {
    // First get the make_id for the requested make
    $makeUrl = "https://carapi.app/api/makes?filter[name]=" . urlencode($make);
    $makeCh = curl_init($makeUrl);
    curl_setopt($makeCh, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($makeCh, CURLOPT_HTTPHEADER, $headers);
    $makeResponse = curl_exec($makeCh);
    curl_close($makeCh);
    
    $makeData = json_decode($makeResponse, true);
    $makeId = null;
    
    // Find the make_id
    if (isset($makeData['data']) && !empty($makeData['data'])) {
        foreach ($makeData['data'] as $makeInfo) {
            if (strcasecmp($makeInfo['name'], $make) === 0) {
                $makeId = $makeInfo['id'];
                break;
            }
        }
    }
    
    if ($makeId) {
        // Get models for this make_id and year with maximum limit
        $modelsUrl = "https://carapi.app/api/models?filter[make_id]=$makeId&filter[year]=$year&sort=name&limit=1000";
        
        $modelsCh = curl_init($modelsUrl);
        curl_setopt($modelsCh, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($modelsCh, CURLOPT_HTTPHEADER, $headers);
        $modelsResponse = curl_exec($modelsCh);
        curl_close($modelsCh);
        
        $modelsData = json_decode($modelsResponse, true);
        
        // Check if there are more pages
        $allModels = [];
        if (isset($modelsData['data'])) {
            $allModels = $modelsData['data'];
            
            // Check if there are more pages
            if (isset($modelsData['collection']['pages']) && $modelsData['collection']['pages'] > 1) {
                $totalPages = min(10, $modelsData['collection']['pages']); // Limit to 10 pages to avoid excessive requests
                
                // Fetch additional pages
                for ($page = 2; $page <= $totalPages; $page++) {
                    $nextPageUrl = "https://carapi.app/api/models?filter[make_id]=$makeId&filter[year]=$year&sort=name&limit=1000&page=$page";
                    
                    $nextPageCh = curl_init($nextPageUrl);
                    curl_setopt($nextPageCh, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($nextPageCh, CURLOPT_HTTPHEADER, $headers);
                    $nextPageResponse = curl_exec($nextPageCh);
                    curl_close($nextPageCh);
                    
                    $nextPageData = json_decode($nextPageResponse, true);
                    
                    if (isset($nextPageData['data']) && !empty($nextPageData['data'])) {
                        $allModels = array_merge($allModels, $nextPageData['data']);
                    }
                }
            }
        }
        
        // Filter to ensure we only get models for this make_id
        $filteredModels = [];
        foreach ($allModels as $model) {
            if ($model['make_id'] == $makeId) {
                $filteredModels[] = $model;
            }
        }
        
        // Return the combined data
        $responseData = [
            'data' => $filteredModels
        ];
        
        header('Content-Type: application/json');
        echo json_encode($responseData);
        exit;
    } else {
        header('Content-Type: application/json');
        echo json_encode(['error' => 'Make not found', 'data' => []]);
        exit;
    }
}

// Special handling for trims endpoint
if ($endpoint === 'trims' && $make && $model && $year) {
    $url = "https://carapi.app/api/trims?filter[make]=" . urlencode($make) . 
           "&filter[model]=" . urlencode($model) . 
           "&filter[year]=" . urlencode($year);
    
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    $response = curl_exec($ch);
    
    if (curl_errno($ch)) {
        header('Content-Type: application/json');
        echo json_encode(['error' => 'API request failed: ' . curl_error($ch)]);
        exit;
    }
    
    curl_close($ch);
    
    // Return the trims data
    header('Content-Type: application/json');
    echo $response;
    exit;
}

// Special handling for bodies endpoint
if ($endpoint === 'bodies' && $make && $model && $year) {
    $url = "https://carapi.app/api/bodies?filter[make]=" . urlencode($make) . 
           "&filter[model]=" . urlencode($model) . 
           "&filter[year]=" . urlencode($year);
    
    // Add verbose parameter if requested
    $verbose = $_GET['verbose'] ?? '';
    if ($verbose === 'yes') {
        $url .= "&verbose=yes";
    }
    
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    $response = curl_exec($ch);
    
    if (curl_errno($ch)) {
        header('Content-Type: application/json');
        echo json_encode(['error' => 'API request failed: ' . curl_error($ch)]);
        exit;
    }
    
    curl_close($ch);
    
    // Return the bodies data
    header('Content-Type: application/json');
    echo $response;
    exit;
}

// Default fallback for other endpoints
$url = "https://carapi.app/api/" . $endpoint;
$query = [];

if ($make) $query['filter[make]'] = $make;
if ($model) $query['filter[model]'] = $model;
if ($year) $query['filter[year]'] = $year;
if ($attribute) $query['attribute'] = $attribute;
if ($model_id) $query['filter[model_id]'] = $model_id;

if (!empty($query)) {
    $url .= '?' . http_build_query($query);
}

$ch = curl_init($url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
$response = curl_exec($ch);

if (curl_errno($ch)) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'API request failed: ' . curl_error($ch)]);
    exit;
}

curl_close($ch);

header('Content-Type: application/json');
echo $response;
?>

