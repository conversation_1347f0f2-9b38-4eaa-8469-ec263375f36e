<?php
$endpoint = $_GET['endpoint'] ?? '';
$make = $_GET['make'] ?? '';
$model = $_GET['model'] ?? '';
$year = $_GET['year'] ?? '';
$attribute = $_GET['attribute'] ?? '';
$model_id = $_GET['model_id'] ?? '';

// API credentials
$apiToken = "c0ac9104-2b74-4f58-a338-d354a75cc894";
$apiSecret = "ae3df83d3eee3268f658cc446cc5cc30";

// JWT cache file path
$jwtCachePath = __DIR__ . '/cache/carapi_jwt.txt';
$jwtCacheDir = dirname($jwtCachePath);

// Create cache directory if it doesn't exist
if (!file_exists($jwtCacheDir)) {
    mkdir($jwtCacheDir, 0755, true);
}

// Get or generate JWT token
function getJwtToken($apiToken, $apiSecret, $jwtCachePath) {
    // Check if we have a cached token that's still valid
    if (file_exists($jwtCachePath)) {
        $jwt = file_get_contents($jwtCachePath);
        // Check if token is still valid (not expired)
        $fileTime = filemtime($jwtCachePath);
        if (time() - $fileTime < 86400) { // 24 hours
            return $jwt;
        }
    }
    
    // Get a new token
    $ch = curl_init('https://carapi.app/api/auth/login');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: text/plain'
    ]);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
        'api_token' => $apiToken,
        'api_secret' => $apiSecret
    ]));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $jwt = curl_exec($ch);
    curl_close($ch);
    
    // Cache the token
    if ($jwt) {
        file_put_contents($jwtCachePath, $jwt);
    }
    
    return $jwt;
}

$jwt = getJwtToken($apiToken, $apiSecret, $jwtCachePath);

// Set up the request headers with JWT authentication
$headers = [
    "Accept: application/json",
    "Authorization: Bearer " . $jwt
];

// Test endpoint to verify API connection
if ($endpoint === 'test') {
    // Test the API connection
    $testUrl = "https://carapi.app/api/makes?limit=5";
    
    $ch = curl_init($testUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    $response = curl_exec($ch);
    
    $info = curl_getinfo($ch);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 'test',
        'url' => $testUrl,
        'http_code' => $info['http_code'],
        'error' => $error,
        'response_sample' => substr($response, 0, 500) . '...',
        'jwt' => substr($jwt, 0, 20) . '...'
    ]);
    exit;
}

// Special handling for makes endpoint with year filter
if ($endpoint === 'makes' && !empty($year)) {
    // Log the request for debugging
    error_log("CarAPI: Fetching makes for year $year");
    
    // Get all models for the specified year
    $modelsUrl = "https://carapi.app/api/models?filter[year]=$year&limit=1000";
    error_log("CarAPI: Models URL: $modelsUrl");
    
    $ch = curl_init($modelsUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    $response = curl_exec($ch);
    
    if (curl_errno($ch)) {
        $error = curl_error($ch);
        error_log("CarAPI: cURL error: $error");
        header('Content-Type: application/json');
        echo json_encode(['error' => 'API request failed: ' . $error]);
        exit;
    }
    
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    error_log("CarAPI: Models API response code: $httpCode");
    
    curl_close($ch);
    
    $modelsData = json_decode($response, true);
    error_log("CarAPI: Models count: " . (isset($modelsData['data']) ? count($modelsData['data']) : 0));
    
    $makeIds = [];
    
    // Extract unique make_ids from the models
    if (isset($modelsData['data']) && !empty($modelsData['data'])) {
        foreach ($modelsData['data'] as $model) {
            if (isset($model['make_id'])) {
                $makeIds[$model['make_id']] = true;
            }
        }
        
        error_log("CarAPI: Unique make_ids found: " . count($makeIds));
        
        // If we have make_ids, get the details for each make
        if (!empty($makeIds)) {
            $makeIds = array_keys($makeIds);
            $makesData = ['data' => []];
            
            // Get details for each make_id
            foreach ($makeIds as $makeId) {
                $makeUrl = "https://carapi.app/api/makes/$makeId";
                error_log("CarAPI: Fetching make details: $makeUrl");
                
                $makeCh = curl_init($makeUrl);
                curl_setopt($makeCh, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($makeCh, CURLOPT_HTTPHEADER, $headers);
                $makeResponse = curl_exec($makeCh);
                $makeHttpCode = curl_getinfo($makeCh, CURLINFO_HTTP_CODE);
                curl_close($makeCh);
                
                error_log("CarAPI: Make API response code: $makeHttpCode");
                
                $makeData = json_decode($makeResponse, true);
                
                if (isset($makeData['data'])) {
                    $makesData['data'][] = $makeData['data'];
                    error_log("CarAPI: Added make: " . $makeData['data']['name']);
                }
            }
            
            error_log("CarAPI: Total makes found: " . count($makesData['data']));
            
            // Return the makes data
            header('Content-Type: application/json');
            echo json_encode($makesData);
            exit;
        }
    }
    
    // If no models found or no makes extracted, return empty data
    error_log("CarAPI: No makes found for year $year");
    header('Content-Type: application/json');
    echo json_encode(['data' => []]);
    exit;
}

// Special handling for models endpoint
if ($endpoint === 'models' && $make && $year) {
    // First get the make_id for the requested make
    $makeUrl = "https://carapi.app/api/makes?filter[name]=" . urlencode($make);
    $makeCh = curl_init($makeUrl);
    curl_setopt($makeCh, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($makeCh, CURLOPT_HTTPHEADER, $headers);
    $makeResponse = curl_exec($makeCh);
    curl_close($makeCh);
    
    $makeData = json_decode($makeResponse, true);
    $makeId = null;
    
    // Find the make_id
    if (isset($makeData['data']) && !empty($makeData['data'])) {
        foreach ($makeData['data'] as $makeInfo) {
            if (strcasecmp($makeInfo['name'], $make) === 0) {
                $makeId = $makeInfo['id'];
                break;
            }
        }
    }
    
    if ($makeId) {
        // Create a JSON query for the specific make_id and year
        $jsonQuery = urlencode(json_encode([
            ["field" => "make_id", "op" => "=", "val" => (int)$makeId],
            ["field" => "year", "op" => "=", "val" => (int)$year]
        ]));
        
        // Get models for this make_id and year with maximum limit
        $modelsUrl = "https://carapi.app/api/models?json={$jsonQuery}&sort=name&limit=1000";
        
        $modelsCh = curl_init($modelsUrl);
        curl_setopt($modelsCh, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($modelsCh, CURLOPT_HTTPHEADER, $headers);
        $modelsResponse = curl_exec($modelsCh);
        curl_close($modelsCh);
        
        // Return the models data
        header('Content-Type: application/json');
        echo $modelsResponse;
        exit;
    } else {
        header('Content-Type: application/json');
        echo json_encode(['error' => 'Make not found', 'data' => []]);
        exit;
    }
}

// Special handling for trims endpoint
if ($endpoint === 'trims' && $make && $model && $year) {
    $url = "https://carapi.app/api/trims?filter[make]=" . urlencode($make) . 
           "&filter[model]=" . urlencode($model) . 
           "&filter[year]=" . urlencode($year);
    
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    $response = curl_exec($ch);
    
    if (curl_errno($ch)) {
        header('Content-Type: application/json');
        echo json_encode(['error' => 'API request failed: ' . curl_error($ch)]);
        exit;
    }
    
    curl_close($ch);
    
    // Return the trims data
    header('Content-Type: application/json');
    echo $response;
    exit;
}

// Special handling for bodies endpoint
if ($endpoint === 'bodies' && $make && $model && $year) {
    // Build a more comprehensive JSON query to get better results
    $jsonQuery = urlencode(json_encode([
        ["field" => "make", "op" => "=", "val" => $make],
        ["field" => "model", "op" => "=", "val" => $model],
        ["field" => "year", "op" => "=", "val" => (int)$year]
    ]));
    
    $url = "https://carapi.app/api/bodies?json={$jsonQuery}&verbose=yes&limit=10";
    
    error_log("CarAPI: Fetching bodies with URL: $url");
    
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    $response = curl_exec($ch);
    
    if (curl_errno($ch)) {
        $error = curl_error($ch);
        error_log("CarAPI: cURL error: $error");
        header('Content-Type: application/json');
        echo json_encode(['error' => 'API request failed: ' . $error]);
        exit;
    }
    
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    error_log("CarAPI: Bodies API response code: $httpCode");
    
    curl_close($ch);
    
    // Parse the response to check for data
    $bodyData = json_decode($response, true);
    
    // If no body data found, try a more relaxed query
    if (empty($bodyData['data']) || count($bodyData['data']) === 0) {
        error_log("CarAPI: No body data found with exact match, trying relaxed query");
        
        // Try a more relaxed query with just make_model_id
        // First get the make_model_id
        $makeModelUrl = "https://carapi.app/api/models?filter[make]=" . urlencode($make) . 
                        "&filter[name]=" . urlencode($model) . 
                        "&filter[year]=" . urlencode($year) . 
                        "&limit=1";
        
        $mmCh = curl_init($makeModelUrl);
        curl_setopt($mmCh, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($mmCh, CURLOPT_HTTPHEADER, $headers);
        $mmResponse = curl_exec($mmCh);
        curl_close($mmCh);
        
        $mmData = json_decode($mmResponse, true);
        
        if (!empty($mmData['data']) && count($mmData['data']) > 0) {
            $makeModelId = $mmData['data'][0]['id'] ?? null;
            
            if ($makeModelId) {
                error_log("CarAPI: Found make_model_id: $makeModelId");
                
                // Use make_model_id in a new JSON query
                $relaxedJsonQuery = urlencode(json_encode([
                    ["field" => "make_model_id", "op" => "=", "val" => (int)$makeModelId]
                ]));
                
                $relaxedUrl = "https://carapi.app/api/bodies?json={$relaxedJsonQuery}&verbose=yes&limit=10";
                error_log("CarAPI: Trying relaxed query: $relaxedUrl");
                
                $relaxedCh = curl_init($relaxedUrl);
                curl_setopt($relaxedCh, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($relaxedCh, CURLOPT_HTTPHEADER, $headers);
                $relaxedResponse = curl_exec($relaxedCh);
                curl_close($relaxedCh);
                
                $relaxedData = json_decode($relaxedResponse, true);
                
                if (!empty($relaxedData['data']) && count($relaxedData['data']) > 0) {
                    error_log("CarAPI: Found " . count($relaxedData['data']) . " bodies with relaxed query");
                    $response = $relaxedResponse;
                } else {
                    // If still no results, try to get body info from trims
                    error_log("CarAPI: No bodies found with relaxed query, trying trims");
                    
                    // Get trims for this make_model_id
                    $trimsUrl = "https://carapi.app/api/trims?filter[make_model_id]=$makeModelId&verbose=yes&limit=10";
                    
                    $trimsCh = curl_init($trimsUrl);
                    curl_setopt($trimsCh, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($trimsCh, CURLOPT_HTTPHEADER, $headers);
                    $trimsResponse = curl_exec($trimsCh);
                    curl_close($trimsCh);
                    
                    $trimsData = json_decode($trimsResponse, true);
                    
                    if (!empty($trimsData['data']) && count($trimsData['data']) > 0) {
                        error_log("CarAPI: Found " . count($trimsData['data']) . " trims");
                        
                        // Create a synthetic body response based on the first trim
                        $trimData = $trimsData['data'][0];
                        $bodyType = "Unknown";
                        
                        // Try to determine body type from trim name or style
                        $trimName = strtolower($trimData['name'] ?? '');
                        $trimStyle = strtolower($trimData['style'] ?? '');
                        
                        if (strpos($trimName, 'suv') !== false || strpos($trimStyle, 'suv') !== false) {
                            $bodyType = "SUV";
                        } else if (strpos($trimName, 'sedan') !== false || strpos($trimStyle, 'sedan') !== false) {
                            $bodyType = "Sedan";
                        } else if (strpos($trimName, 'truck') !== false || strpos($trimStyle, 'truck') !== false || 
                                  strpos($trimName, 'pickup') !== false || strpos($trimStyle, 'pickup') !== false) {
                            $bodyType = "Pickup Truck";
                        } else if (strpos($trimName, 'van') !== false || strpos($trimStyle, 'van') !== false) {
                            $bodyType = "Van";
                        } else if (strpos($trimName, 'coupe') !== false || strpos($trimStyle, 'coupe') !== false) {
                            $bodyType = "Coupe";
                        } else if (strpos($trimName, 'convertible') !== false || strpos($trimStyle, 'convertible') !== false) {
                            $bodyType = "Convertible";
                        } else if (strpos($trimName, 'hatchback') !== false || strpos($trimStyle, 'hatchback') !== false) {
                            $bodyType = "Hatchback";
                        } else if (strpos($trimName, 'wagon') !== false || strpos($trimStyle, 'wagon') !== false) {
                            $bodyType = "Wagon";
                        }
                        
                        // Create synthetic body data
                        $syntheticBody = [
                            'data' => [
                                [
                                    'id' => 0,
                                    'make_model_trim_id' => $trimData['id'] ?? 0,
                                    'type' => $bodyType,
                                    'doors' => 4, // Default to 4 doors
                                    'seats' => 5, // Default to 5 seats
                                    'year' => (int)$year,
                                    'make' => $make,
                                    'model' => $model
                                ]
                            ]
                        ];
                        
                        error_log("CarAPI: Created synthetic body data with type: $bodyType");
                        $response = json_encode($syntheticBody);
                    }
                }
            }
        }
    }
    
    // Return the bodies data
    header('Content-Type: application/json');
    echo $response;
    exit;
}

// Special handling for makes-by-year endpoint
if ($endpoint === 'makes-by-year' && !empty($year)) {
    // Log the request for debugging
    error_log("CarAPI: Fetching makes for year $year");
    
    // Get all models for the specified year
    $modelsUrl = "https://carapi.app/api/models?filter[year]=$year&limit=1000";
    error_log("CarAPI: Models URL: $modelsUrl");
    
    $ch = curl_init($modelsUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    $response = curl_exec($ch);
    
    if (curl_errno($ch)) {
        $error = curl_error($ch);
        error_log("CarAPI: cURL error: $error");
        header('Content-Type: application/json');
        echo json_encode(['error' => 'API request failed: ' . $error]);
        exit;
    }
    
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    error_log("CarAPI: Models API response code: $httpCode");
    
    curl_close($ch);
    
    $modelsData = json_decode($response, true);
    
    // Extract unique make_ids from the models
    $makeIds = [];
    if (isset($modelsData['data']) && !empty($modelsData['data'])) {
        error_log("CarAPI: Found " . count($modelsData['data']) . " models for year $year");
        
        foreach ($modelsData['data'] as $model) {
            if (isset($model['make_id']) && !empty($model['make_id'])) {
                $makeIds[$model['make_id']] = true;
            }
        }
        
        error_log("CarAPI: Extracted " . count($makeIds) . " unique make_ids");
        
        if (!empty($makeIds)) {
            // Prepare data structure for makes
            $makesData = ['data' => []];
            
            // Get details for each make_id
            foreach (array_keys($makeIds) as $makeId) {
                $makeUrl = "https://carapi.app/api/makes/$makeId";
                error_log("CarAPI: Fetching make details: $makeUrl");
                
                $makeCh = curl_init($makeUrl);
                curl_setopt($makeCh, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($makeCh, CURLOPT_HTTPHEADER, $headers);
                $makeResponse = curl_exec($makeCh);
                $makeHttpCode = curl_getinfo($makeCh, CURLINFO_HTTP_CODE);
                curl_close($makeCh);
                
                error_log("CarAPI: Make API response code: $makeHttpCode");
                
                $makeData = json_decode($makeResponse, true);
                
                if (isset($makeData['data'])) {
                    $makesData['data'][] = $makeData['data'];
                    error_log("CarAPI: Added make: " . $makeData['data']['name']);
                }
            }
            
            error_log("CarAPI: Total makes found: " . count($makesData['data']));
            
            // Return the makes data
            header('Content-Type: application/json');
            echo json_encode($makesData);
            exit;
        }
    }
    
    // If no models found or no makes extracted, return empty data
    error_log("CarAPI: No makes found for year $year");
    header('Content-Type: application/json');
    echo json_encode(['data' => []]);
    exit;
}

// Special handling for models endpoint with year parameter
if ($endpoint === 'models' && !empty($year) && empty($make)) {
    // Log the request for debugging
    error_log("CarAPI: Fetching models for year $year");
    
    // Create a JSON query for the specific year
    $jsonQuery = urlencode(json_encode([
        ["field" => "year", "op" => "=", "val" => (int)$year]
    ]));
    
    // Get all models for the specified year using JSON query
    $modelsUrl = "https://carapi.app/api/models?json={$jsonQuery}&limit=1000";
    error_log("CarAPI: Models URL: $modelsUrl");
    
    $ch = curl_init($modelsUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    $response = curl_exec($ch);
    
    if (curl_errno($ch)) {
        $error = curl_error($ch);
        error_log("CarAPI: cURL error: $error");
        header('Content-Type: application/json');
        echo json_encode(['error' => 'API request failed: ' . $error]);
        exit;
    }
    
    curl_close($ch);
    
    // Return the models data
    header('Content-Type: application/json');
    echo $response;
    exit;
}

// Default fallback for other endpoints
$url = "https://carapi.app/api/" . $endpoint;
$query = [];

if ($make) $query['filter[make]'] = $make;
if ($model) $query['filter[model]'] = $model;
if ($year) $query['filter[year]'] = $year;
if ($attribute) $query['attribute'] = $attribute;
if ($model_id) $query['filter[model_id]'] = $model_id;

if (!empty($query)) {
    $url .= '?' . http_build_query($query);
}

$ch = curl_init($url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
$response = curl_exec($ch);

if (curl_errno($ch)) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'API request failed: ' . curl_error($ch)]);
    exit;
}

curl_close($ch);

header('Content-Type: application/json');
echo $response;
?>

