<?php
// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Generate a unique quote ID if not already set
if (!isset($_SESSION['quote_id'])) {
    $_SESSION['quote_id'] = 'QUOTE-' . bin2hex(random_bytes(6));
}

// Get vehicle information from session
$vehicle_year = $_SESSION['vehicle_year'] ?? '2014';
$vehicle_brand = $_SESSION['vehicle_brand'] ?? 'Honda';
$vehicle_model = $_SESSION['vehicle_model'] ?? 'CR-V';

// Get location information
$origin_city = $_SESSION['origin_city'] ?? 'Miami';
$origin_state = $_SESSION['origin_state'] ?? 'FL';
$origin_postal_code = $_SESSION['origin_postal_code'] ?? '33266';

$destination_city = $_SESSION['destination_city'] ?? 'Orlando';
$destination_state = $_SESSION['destination_state'] ?? 'FL';
$destination_postal_code = $_SESSION['destination_postal_code'] ?? '32869';

// Get transport details
$transport_type = isset($_SESSION['transport_type']) ? ($_SESSION['transport_type'] == 1 ? 'Open Carrier' : 'Enclosed Carrier') : 'Open Carrier';
$vehicle_condition = isset($_SESSION['vehicle_inop']) ? ($_SESSION['vehicle_inop'] == 0 ? 'Operable' : 'Inoperable') : 'Operable';

// Estimated transit time (can be calculated based on distance or set as default)
$estimated_transit = "2-3 Days";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shipment Summary</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f7;
            margin: 0;
            padding: 0;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 40px auto;
            padding: 0;
        }
        .shipment-card {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
            overflow: hidden;
            margin-bottom: 30px;
        }
        .shipment-header {
            background-color: #f5f5f7;
            padding: 20px 25px;
            border-bottom: 1px solid #e1e1e1;
        }
        .shipment-header h2 {
            margin: 0;
            font-size: 24px;
            color: #222a31;
            font-weight: 600;
        }
        .shipment-content {
            padding: 0;
        }
        .summary-row {
            display: flex;
            justify-content: space-between;
            padding: 18px 25px;
            border-bottom: 1px solid #eee;
        }
        .summary-row:last-child {
            border-bottom: none;
        }
        .summary-label {
            font-size: 16px;
            color: #666;
        }
        .summary-value {
            font-size: 16px;
            font-weight: 500;
            color: #222a31;
            text-align: right;
        }
        .route-info {
            display: flex;
            align-items: center;
            margin: 20px 0;
            padding: 0 25px;
        }
        .location {
            display: flex;
            align-items: center;
            background-color: #f8f9fa;
            padding: 10px 15px;
            border-radius: 20px;
            margin-right: 10px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }
        .location i {
            color: #cda565;
            margin-right: 8px;
        }
        .arrow {
            margin: 0 15px;
            color: #999;
            font-size: 18px;
        }
        .order-title {
            font-size: 28px;
            font-weight: 700;
            margin: 0 0 20px 0;
            color: #222a31;
            padding: 0 25px;
        }
        @media (max-width: 768px) {
            .container {
                margin: 20px auto;
                padding: 0 15px;
            }
            .shipment-header {
                padding: 15px 20px;
            }
            .shipment-header h2 {
                font-size: 20px;
            }
            .summary-row {
                padding: 15px 20px;
            }
            .summary-label, .summary-value {
                font-size: 14px;
            }
            .order-title {
                font-size: 22px;
                padding: 0 20px;
            }
            .route-info {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
            .location {
                width: 100%;
                box-sizing: border-box;
            }
            .arrow {
                transform: rotate(90deg);
                margin: 5px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="shipment-card">
            <h1 class="order-title">Shipment Summary:</h1>
            
            <div class="shipment-content">
                <div class="summary-row">
                    <div class="summary-label">Quote ID</div>
                    <div class="summary-value"><?php echo htmlspecialchars($_SESSION['quote_id']); ?></div>
                </div>
                
                <div class="summary-row">
                    <div class="summary-label">Pickup From</div>
                    <div class="summary-value"><?php echo htmlspecialchars("$origin_city, $origin_state $origin_postal_code"); ?></div>
                </div>
                
                <div class="summary-row">
                    <div class="summary-label">Deliver To</div>
                    <div class="summary-value"><?php echo htmlspecialchars("$destination_city, $destination_state $destination_postal_code"); ?></div>
                </div>
                
                <div class="summary-row">
                    <div class="summary-label">Vehicle</div>
                    <div class="summary-value"><?php echo htmlspecialchars("$vehicle_year $vehicle_brand $vehicle_model"); ?></div>
                </div>
                
                <div class="summary-row">
                    <div class="summary-label">Transport Type</div>
                    <div class="summary-value"><?php echo htmlspecialchars($transport_type); ?></div>
                </div>
                
                <div class="summary-row">
                    <div class="summary-label">Condition</div>
                    <div class="summary-value"><?php echo htmlspecialchars($vehicle_condition); ?></div>
                </div>
                
                <div class="summary-row">
                    <div class="summary-label">Estimated Transit</div>
                    <div class="summary-value"><?php echo htmlspecialchars($estimated_transit); ?></div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
