// 1. Populate Vehicle Years
function populateYears() {
    const yearSelect = document.getElementById("vehicle_year");
    // Add years from 1940 to current year
    const currentYear = new Date().getFullYear();
    for (let year = currentYear; year >= 1940; year--) {
        const option = document.createElement("option");
        option.value = year;
        option.textContent = year;
        yearSelect.appendChild(option);
    }
}

// 2. Populate Vehicle Brands
function populateBrands() {
    const brandSelect = document.getElementById("vehicle_brand");
    brandSelect.innerHTML = '<option value="" disabled selected>Vehicle brand</option>';
    brandSelect.disabled = true; // Disable until year is selected
    
    fetch('carapi-proxy.php?endpoint=makes')
        .then(response => response.json())
        .then(data => {
            if (data.data && data.data.length > 0) {
                // Sort brands alphabetically
                const brands = data.data
                    .filter(make => make.name && make.name.length > 1) // Filter out short or invalid names
                    .map(make => ({
                        id: make.id,
                        name: make.name,
                        fullName: make.name // Store full name for display
                    }))
                    .sort((a, b) => a.name.localeCompare(b.name));
                
                brands.forEach(brand => {
                    const option = document.createElement("option");
                    option.value = brand.name; // Use name as value
                    option.textContent = brand.fullName; // Display full name
                    option.dataset.id = brand.id; // Store ID as data attribute
                    brandSelect.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('Error fetching vehicle brands:', error);
        });
}

// 3. Populate Vehicle Models and Get Vehicle Type
function populateModels(brand, year) {
    const modelSelect = document.getElementById("vehicle_model");
    modelSelect.innerHTML = '<option value="" disabled selected>Vehicle model</option>'; 
    modelSelect.disabled = true;
    
    if (brand && year) {
        console.log(`Fetching models for brand: ${brand}, year: ${year}`);
        // Add timestamp to prevent caching
        const timestamp = new Date().getTime();
        
        // First ensure we have a valid token
        fetch(`carapi-proxy.php?endpoint=auth&_=${timestamp}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Auth error! Status: ${response.status}`);
                }
                console.log("Authentication successful for models request");
                
                // Now fetch models with a fresh token
                return fetch(`carapi-proxy.php?endpoint=models&make=${encodeURIComponent(brand)}&year=${encodeURIComponent(year)}&_=${timestamp}`);
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log("Models API response:", data); // Debug log
                modelSelect.innerHTML = '<option value="" disabled selected>Vehicle model</option>';
                
                if (data.data && data.data.length > 0) {
                    // Sort models alphabetically and filter out invalid models
                    const models = data.data
                        .filter(model => model.name && model.name.length > 1) // Filter out short or invalid names
                        .map(model => model.name)
                        .sort();
                    
                    // Add unique models to dropdown
                    const uniqueModels = [...new Set(models)];
                    uniqueModels.forEach(modelName => {
                        const option = document.createElement("option");
                        option.value = modelName;
                        option.textContent = modelName;
                        modelSelect.appendChild(option);
                    });
                    modelSelect.disabled = false;
                    console.log(`Populated ${uniqueModels.length} models`);
                } else {
                    // If no models found, add a placeholder
                    const option = document.createElement("option");
                    option.value = "";
                    option.textContent = "No models found";
                    option.disabled = true;
                    option.selected = true;
                    modelSelect.appendChild(option);
                    console.log("No models found for this brand/year");
                }
            })
            .catch(error => {
                console.error('Error fetching vehicle models:', error);
                // Add error placeholder
                modelSelect.innerHTML = '<option value="" disabled selected>Error loading models</option>';
            });
    } else {
        modelSelect.innerHTML = '<option value="" disabled selected>Please select brand and year first</option>';
    }
}

// Get Vehicle Type
function getVehicleType(year, brand, model) {
    return new Promise((resolve, reject) => {
        // Add timestamp to prevent caching
        const timestamp = new Date().getTime();
        
        // First ensure we have a valid token
        fetch(`carapi-proxy.php?endpoint=auth&_=${timestamp}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Auth error! Status: ${response.status}`);
                }
                console.log("Authentication successful for vehicle type request");
                
                // Now fetch bodies with verbose=yes parameter to get detailed info
                return fetch(`carapi-proxy.php?endpoint=bodies&make=${encodeURIComponent(brand)}&model=${encodeURIComponent(model)}&year=${encodeURIComponent(year)}&verbose=yes&_=${timestamp}`);
            })
            .then(response => response.json())
            .then(data => {
                console.log("Bodies API response:", data); // Debug log
                let bodyType = "Unknown";
                
                // Extract type directly from the body data
                if (data.data && data.data.length > 0) {
                    // Use the first body type found
                    bodyType = data.data[0].type || "Unknown";
                    console.log("Found body type:", bodyType);
                }
                
                // Map body types to the required categories
                let categoryType = "Car"; // Default category
                
                // Car types
                if (["Convertible", "Coupe", "Sedan", "Wagon"].includes(bodyType)) {
                    categoryType = "Car";
                }
                // SUV types
                else if (["SUV", "Hatchback"].includes(bodyType)) {
                    categoryType = "SUV";
                }
                // Pickup types - check if it contains "Truck"
                else if (bodyType.includes("Truck")) {
                    categoryType = "Pickup";
                }
                // Van types - check if it contains "Van" or "Minivan"
                else if (bodyType.includes("Van") || bodyType.includes("Minivan")) {
                    categoryType = "Van";
                }
                
                // Return both the original body type and the mapped category
                resolve({
                    originalType: bodyType,
                    categoryType: categoryType
                });
            })
            .catch(error => {
                console.error('Error fetching vehicle type:', error);
                resolve({
                    originalType: "Unknown",
                    categoryType: "Car" // Default to Car on error
                });
            });
    });
}

// 4. Enable/Disable Dropdowns and Get Vehicle Type
document.addEventListener("DOMContentLoaded", function() {
    populateYears();
    populateBrands();

    const yearSelect = document.getElementById("vehicle_year");
    const brandSelect = document.getElementById("vehicle_brand");
    const modelSelect = document.getElementById("vehicle_model");
    
    // Initially disable brand and model selects
    brandSelect.disabled = true;
    modelSelect.disabled = true;
    
    // Create hidden input field for vehicle_type
    const vehicleTypeField = document.createElement("input");
    vehicleTypeField.type = "hidden";
    vehicleTypeField.id = "vehicle_type";
    vehicleTypeField.name = "vehicle_type"; // This name will be used in the form submission
    document.getElementById("transportForm").appendChild(vehicleTypeField);

    yearSelect.addEventListener("change", function() {
        brandSelect.disabled = false;
        modelSelect.disabled = true;
        // Clear model selection when year changes
        modelSelect.innerHTML = '<option value="" disabled selected>Vehicle model</option>';
    });

    brandSelect.addEventListener("change", function() {
        const selectedBrand = brandSelect.value;
        const selectedYear = yearSelect.value;
        populateModels(selectedBrand, selectedYear);
    });
    
    modelSelect.addEventListener("change", function() {
        const selectedYear = yearSelect.value;
        const selectedBrand = brandSelect.value;
        const selectedModel = modelSelect.value;
        
        if (selectedYear && selectedBrand && selectedModel) {
            getVehicleType(selectedYear, selectedBrand, selectedModel)
                .then(typeData => {
                    // Set the value of the hidden input field to the category type for submission
                    vehicleTypeField.value = typeData.categoryType;
                    console.log("Vehicle Type:", typeData.categoryType);
                    
                    // Display both the original body type and the category
                    const typeDisplay = document.getElementById("vehicle_type_display");
                    if (typeDisplay) {
                        typeDisplay.innerHTML = `Body Type: <strong>${typeData.originalType}</strong><br>Vehicle Category: <strong>${typeData.categoryType}</strong>`;
                    } else {
                        const typeDiv = document.createElement("div");
                        typeDiv.id = "vehicle_type_display";
                        typeDiv.innerHTML = `Body Type: <strong>${typeData.originalType}</strong><br>Vehicle Category: <strong>${typeData.categoryType}</strong>`;
                        typeDiv.style.marginTop = "10px";
                        modelSelect.parentNode.appendChild(typeDiv);
                    }
                });
        }
    });
});

// 5. City Validation
function isValidCity(city) {
    // ქალაქის სახელი უნდა შედგებოდეს მინიმუმ 2 ასოსგან
    const cityPattern = /^[A-Za-z\s]{2,}$/;
    return cityPattern.test(city);
}

// 6. ZIP Code Validation
function isValidZipCode(zipCode) {
    // ზიპ კოდი უნდა შედგებოდეს ზუსტად 5 ციფრისგან
    const zipCodePattern = /^\d{5}$/;
    return zipCodePattern.test(zipCode);
}

// 7. Location Validation
function isValidLocation(location) {
    // დაყოფა city და zipCode-ად
    const parts = location.split(",").map(part => part.trim());
    const city = parts[0];
    const state = parts[1]?.split(" ")[0]; // შტატის აბრევიატურა
    const zipCode = parts[1]?.split(" ")[1]; // ზიპ კოდი
    const country = parts[2]; // ქვეყანა

    // ქალაქი უნდა იყოს სწორად შეყვანილი
    const isCityValid = isValidCity(city);

    // ზიპ კოდი უნდა იყოს სწორად შეყვანილი (თუ არსებობს)
    const isZipCodeValid = !zipCode || isValidZipCode(zipCode);

    // ქვეყანა უნდა იყოს სწორად შეყვანილი
    const isCountryValid = country && /^[A-Za-z\s]+$/.test(country);

    // ან ქალაქი, ან ზიპ კოდი უნდა იყოს სწორად შეყვანილი
    return (isCityValid || isZipCodeValid) && isCountryValid;
}

// 8. Step 1 Validation
function isStepOneValid() {
    let isValid = true;

    // Transport From Validation
    const from = document.getElementById("transport_from").value.trim();
    const fromWrapper = document.getElementById("transport_from").closest(".input-wrapper");
    if (!isValidLocation(from)) {
        fromWrapper.classList.add("input-error");
        showNotification("Please enter a valid Zip or City", "error");
        isValid = false;
    } else {
        fromWrapper.classList.remove("input-error");
    }

    // Transport To Validation
    const to = document.getElementById("transport_to").value.trim();
    const toWrapper = document.getElementById("transport_to").closest(".input-wrapper");
    if (!isValidLocation(to)) {
        toWrapper.classList.add("input-error");
        showNotification("Please enter a valid Zip or City", "error");
        isValid = false;
    } else {
        toWrapper.classList.remove("input-error");
    }

    // Transport Type Validation
    const transportType = document.querySelector('input[name="transport_type"]:checked');
    if (!transportType) {
        document.querySelector('input[name="transport_type"]').closest(".rowtransporttyp").classList.add("radio-error");
        showNotification("Please select a transport type", "error");
        isValid = false;
    } else {
        document.querySelector('input[name="transport_type"]').closest(".rowtransporttyp").classList.remove("radio-error");
    }

    return isValid;
}

// 9. Step 2 Validation
function isStepTwoValid() {
    let isValid = true;

    // Vehicle Year Validation
    const vehicleYear = document.getElementById("vehicle_year").value;
    const vehicleYearWrapper = document.getElementById("vehicle_year").closest(".styled-select");
    if (!vehicleYear) {
        vehicleYearWrapper.classList.add("input-error");
        showNotification("Please select a vehicle year", "error");
        isValid = false;
    } else {
        vehicleYearWrapper.classList.remove("input-error");
    }

    // Vehicle Brand Validation
    const vehicleBrand = document.getElementById("vehicle_brand").value;
    const vehicleBrandWrapper = document.getElementById("vehicle_brand").closest(".styled-select");
    if (!vehicleBrand) {
        vehicleBrandWrapper.classList.add("input-error");
        showNotification("Please select a vehicle brand", "error");
        isValid = false;
    } else {
        vehicleBrandWrapper.classList.remove("input-error");
    }

    // Vehicle Model Validation
    const vehicleModel = document.getElementById("vehicle_model").value;
    const vehicleModelWrapper = document.getElementById("vehicle_model").closest(".styled-select");
    if (!vehicleModel) {
        vehicleModelWrapper.classList.add("input-error");
        showNotification("Please select a vehicle model", "error");
        isValid = false;
    } else {
        vehicleModelWrapper.classList.remove("input-error");
    }

    // Operable Validation
    const operable = document.querySelector('input[name="vehicle_operable"]:checked');
    if (!operable) {
        document.querySelector('input[name="vehicle_operable"]').closest(".rowtransporttyp").classList.add("radio-error");
        showNotification("Please select if the vehicle is operable", "error");
        isValid = false;
    } else {
        document.querySelector('input[name="vehicle_operable"]').closest(".rowtransporttyp").classList.remove("radio-error");
    }

    return isValid;
}

// 10. Phone Number Formatting
document.getElementById("phone").addEventListener("input", function() {
    // წაშალე ყველა არა-ციფრული სიმბოლო
    let input = this.value.replace(/\D/g, '');

    // შეზღუდე ნომრის სიგრძე 10 ციფრამდე
    if (input.length > 10) input = input.substring(0, 10);

    // ფორმატირება (XXX) XXX-XXXX
    const formattedPhone = input.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
    this.value = formattedPhone;
});

// 11. Phone Number Validation
function isValidPhoneNumber(phone) {
    // ნომერი უნდა შეესაბამებოდეს ფორმატს (XXX) XXX-XXXX
    const phonePattern = /^\(\d{3}\) \d{3}-\d{4}$/;
    return phonePattern.test(phone);
}

// 12. Step 3 Validation
function isStepThreeValid() {
    let isValid = true;

    // Email Validation
    const email = document.getElementById("email").value.trim();
    const emailWrapper = document.getElementById("email").closest(".input-wrapper");
    if (email === "" || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
        emailWrapper.classList.add("input-error");
        showNotification("Please enter a valid email", "error");
        isValid = false;
    } else {
        emailWrapper.classList.remove("input-error");
    }

    // Available Date Validation
    const availableDate = document.getElementById("available_date").value;
    const dateWrapper = document.getElementById("available_date").closest(".select-custom");
    if (availableDate === "") {
        dateWrapper.classList.add("input-error");
        showNotification("Please select a ship date", "error");
        isValid = false;
    } else {
        dateWrapper.classList.remove("input-error");
    }

    // Name Validation
    const name = document.getElementById("name").value.trim();
    const nameWrapper = document.getElementById("name").closest(".input-wrapper");
    if (name === "") {
        nameWrapper.classList.add("input-error");
        showNotification("Please enter your full name", "error");
        isValid = false;
    } else {
        nameWrapper.classList.remove("input-error");
    }

    // Phone Validation
    const phone = document.getElementById("phone").value.trim();
    const phoneWrapper = document.getElementById("phone").closest(".input-wrapper");
    if (!isValidPhoneNumber(phone)) {
        phoneWrapper.classList.add("input-error");
        showNotification("Please enter a valid phone number", "error");
        isValid = false;
    } else {
        phoneWrapper.classList.remove("input-error");
    }

    return isValid;
}

// 2. Phone Number Formatting
document.getElementById("phone").addEventListener("input", function() {
    // წაშალე ყველა არა-ციფრული სიმბოლო
    let input = this.value.replace(/\D/g, '');

    // შეზღუდე ნომრის სიგრძე 10 ციფრამდე
    if (input.length > 10) input = input.substring(0, 10);

    // ფორმატირება (XXX) XXX-XXXX
    const formattedPhone = input.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
    this.value = formattedPhone;
});

// 3. Phone Number Validation
function isValidPhoneNumber(phone) {
    // ნომერი უნდა შეესაბამებოდეს ფორმატს (XXX) XXX-XXXX
    const phonePattern = /^\(\d{3}\) \d{3}-\d{4}$/;
    return phonePattern.test(phone);
}

// 8. Allow Only Latin Characters
function allowOnlyLatin(event) {
    const charCode = event.charCode || event.keyCode;
    const char = String.fromCharCode(charCode);
    const latinRegex = /^[A-Za-z0-9@.,'"\-() ]+$/; // დასაშვები სიმბოლოები

    // თუ სიმბოლო არ შეესაბამება ლათინურ ასოებს ან რიცხვებს, აღკვეთე შეყვანა
    if (!latinRegex.test(char) && charCode !== 8 && charCode !== 9) { // 8 = Backspace, 9 = Tab
        event.preventDefault();
    }
}

document.querySelectorAll("#transport_from, #transport_to, #email, #name, #vehicle_brand, #vehicle_model").forEach(input => {
    input.addEventListener("keypress", allowOnlyLatin); // ყოველი კლავიშის დაჭერისას შეამოწმე
    input.addEventListener("input", function(event) {
        // წაშალე ყველა არა-ლათინური სიმბოლო
        this.value = this.value.replace(/[^A-Za-z0-9@.,'"\-() ]/g, '');
    });
});

// 4. Step Navigation
function nextStep(step) {
    if (step === 2 && !isStepOneValid()) {
        return; // არ გაგრძელდება, თუ Step 1 არ არის ვალიდური
    }
    if (step === 3 && !isStepTwoValid()) {
        return; // არ გაგრძელდება, თუ Step 2 არ არის ვალიდური
    }

    document.querySelectorAll(".step").forEach(stepElement => {
        stepElement.style.display = "none";
    });
    document.getElementById(`step-${step}`).style.display = "block";
}


// 5. Form Submission
document.getElementById("transportForm").addEventListener("submit", function(event) {
    if (isStepThreeValid()) { 
        this.submit(); // Directly submit the form
    } else {
        showNotification("Please fill in all required fields.", "error");
        event.preventDefault(); // Prevent form submission if validation fails
    }
});

// 6. Show Notification
function showNotification(message, type) {
    const notification = document.getElementById("notification");
    if (notification) {
        notification.innerText = message;
        notification.className = `${type}-message notification`;
        notification.style.display = "block";
        setTimeout(function() {
            notification.style.display = "none";
        }, 5000); // შეტყობინება გაქრება 5 წამის შემდეგ
    }
}
